import { useState, useEffect, useMemo } from "react";
import { Command } from "cmdk";
import { useNavigate } from "@tanstack/react-router";
import { usePages } from "@/hooks/usePages";
import { useFolders } from "@/hooks/useFolders";
import { useFolderContext } from "@/contexts/FolderContext";
import { Search, FileText, Folder, Clock, Hash } from "lucide-react";
import { cn } from "@/lib/utils";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";

dayjs.extend(relativeTime);

interface SearchCommandProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface SearchResult {
  id: string;
  type: 'folder' | 'drawing';
  title: string;
  subtitle: string;
  icon: React.ReactNode;
  data: any;
}

export function SearchCommand({ open, onOpenChange }: SearchCommandProps) {
  const [search, setSearch] = useState("");
  const { pages } = usePages();
  const { folders } = useFolders();
  const { setSelectedFolderId } = useFolderContext();
  const navigate = useNavigate();

  // Reset search when modal opens/closes
  useEffect(() => {
    if (!open) {
      setSearch("");
    }
  }, [open]);

  useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === "k" && (e.metaKey || e.ctrlKey)) {
        e.preventDefault();
        onOpenChange(!open);
      }
      if (e.key === "Escape" && open) {
        e.preventDefault();
        onOpenChange(false);
      }
    };

    document.addEventListener("keydown", down);
    return () => document.removeEventListener("keydown", down);
  }, [open, onOpenChange]);

  const handlePageSelect = (pageId: string) => {
    onOpenChange(false);
    navigate({ to: "/page/$id", params: { id: pageId } });
  };

  const handleFolderSelect = (folderId: string) => {
    onOpenChange(false);
    setSelectedFolderId(folderId);
    navigate({ to: "/pages" });
  };

  // Enhanced search results with better filtering and sorting
  const searchResults = useMemo(() => {
    if (!search.trim()) return [];

    const query = search.toLowerCase().trim();
    const results: SearchResult[] = [];

    // Search folders
    folders?.forEach((folder) => {
      if (folder.name.toLowerCase().includes(query)) {
        const pageCount = pages?.filter(p => p.folder_id === folder.folder_id).length || 0;
        results.push({
          id: `folder-${folder.folder_id}`,
          type: 'folder',
          title: folder.name,
          subtitle: `${pageCount} drawing${pageCount !== 1 ? 's' : ''}`,
          icon: <Folder className="h-4 w-4 text-accent-blue" />,
          data: folder,
        });
      }
    });

    // Search drawings/pages
    pages?.forEach((page) => {
      const pageName = page.name || "Untitled";
      if (pageName.toLowerCase().includes(query)) {
        const folderName = folders?.find(f => f.folder_id === page.folder_id)?.name || "Unknown";
        results.push({
          id: `drawing-${page.page_id}`,
          type: 'drawing',
          title: pageName,
          subtitle: `in ${folderName} • ${dayjs(page.updated_at).fromNow()}`,
          icon: <FileText className="h-4 w-4 text-text-muted" />,
          data: page,
        });
      }
    });

    // Sort results: folders first, then by relevance (exact matches first)
    return results.sort((a, b) => {
      if (a.type !== b.type) {
        return a.type === 'folder' ? -1 : 1;
      }

      const aExact = a.title.toLowerCase() === query;
      const bExact = b.title.toLowerCase() === query;
      if (aExact !== bExact) {
        return aExact ? -1 : 1;
      }

      const aStarts = a.title.toLowerCase().startsWith(query);
      const bStarts = b.title.toLowerCase().startsWith(query);
      if (aStarts !== bStarts) {
        return aStarts ? -1 : 1;
      }

      return a.title.localeCompare(b.title);
    });
  }, [search, folders, pages]);

  const handleResultSelect = (result: SearchResult) => {
    if (result.type === 'folder') {
      handleFolderSelect(result.data.folder_id);
    } else {
      handlePageSelect(result.data.page_id);
    }
  };

  if (!open) return null;

  return (
    <div
      className="fixed inset-0 z-50 bg-black/60 backdrop-blur-sm"
      onClick={() => onOpenChange(false)}
    >
      <div
        className="fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-2xl mx-4"
        onClick={(e) => e.stopPropagation()}
      >
        <Command
          className="rounded-modal border border-border-subtle bg-background-card overflow-hidden"
          style={{ boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.8)' }}
          shouldFilter={false}
        >
          <div className="flex items-center border-b border-border-subtle px-4 py-3">
            <Search className="mr-3 h-5 w-5 shrink-0 text-text-muted" />
            <Command.Input
              value={search}
              onValueChange={setSearch}
              placeholder="Search folders and drawings..."
              className="flex h-8 w-full bg-transparent text-base outline-none placeholder:text-text-muted disabled:cursor-not-allowed disabled:opacity-50 text-text-primary"
              autoFocus
            />
            {search && (
              <div className="flex items-center gap-2 text-xs text-text-muted">
                <Hash className="h-3 w-3" />
                {searchResults.length}
              </div>
            )}
          </div>

          <Command.List className="max-h-[400px] overflow-y-auto overflow-x-hidden">
            {!search.trim() ? (
              <div className="py-8 text-center">
                <Search className="mx-auto h-8 w-8 text-text-muted mb-3" />
                <p className="text-sm text-text-muted">Start typing to search folders and drawings</p>
                <p className="text-xs text-text-muted mt-1">Use ⌘K to open, Esc to close</p>
              </div>
            ) : searchResults.length === 0 ? (
              <Command.Empty className="py-8 text-center">
                <div className="text-sm text-text-muted">
                  No results found for "{search}"
                </div>
                <div className="text-xs text-text-muted mt-1">
                  Try a different search term
                </div>
              </Command.Empty>
            ) : (
              <div className="p-2">
                {searchResults.map((result, index) => (
                  <Command.Item
                    key={result.id}
                    value={result.id}
                    onSelect={() => handleResultSelect(result)}
                    className={cn(
                      "relative flex cursor-pointer select-none items-center rounded-md px-3 py-3 text-sm outline-none transition-all duration-150",
                      "hover:bg-background-hover",
                      "data-[selected]:bg-background-hover",
                      index > 0 && "mt-1"
                    )}
                  >
                    <div className="mr-3 flex h-8 w-8 items-center justify-center rounded-md bg-background-main">
                      {result.icon}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-text-primary truncate">
                        {result.title}
                      </div>
                      <div className="text-xs text-text-muted truncate flex items-center gap-1">
                        {result.type === 'drawing' && <Clock className="h-3 w-3" />}
                        {result.subtitle}
                      </div>
                    </div>
                    <div className="ml-2 text-xs text-text-muted capitalize bg-background-main px-2 py-1 rounded">
                      {result.type}
                    </div>
                  </Command.Item>
                ))}
              </div>
            )}
          </Command.List>
        </Command>
      </div>
    </div>
  );
}
